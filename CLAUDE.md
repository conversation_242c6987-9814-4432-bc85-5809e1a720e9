# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Spring Boot 2.6.13 的演示项目，集成了多种常用的企业级技术栈：
- MyBatis-Plus（数据库ORM）
- ElasticSearch（搜索引擎）
- XXL-Job（分布式任务调度）
- Knife4j（API文档）
- 动态数据源
- RocketMQ（消息队列）

## 常用命令

### 构建和运行
```bash
# 编译项目
mvn compile

# 运行测试
mvn test

# 打包
mvn package

# 运行应用
mvn spring-boot:run

# 或者运行打包后的jar
java -jar target/boot-demo-1-0.0.1-SNAPSHOT.jar
```

### 测试
```bash
# 运行所有测试
mvn test

# 运行单个测试类
mvn test -Dtest=BootDemo1ApplicationTests

# 运行单个测试方法
mvn test -Dtest=BootDemo1ApplicationTests#contextLoads
```

## 项目架构

### 核心配置
- **ElasticSearchConfig**: 配置ES高级客户端，连接信息通过 `von.es.*` 配置
- **MybatisPlusConfig**: 配置MyBatis-Plus分页插件，支持MySQL数据库
- **XxlJobConfig**: 配置XXL-Job执行器，用于分布式定时任务
- **Knife4jConfiguration**: API文档配置

### 数据源配置
使用动态数据源，支持多数据库：
- `master`: 主数据源（localhost:13306）
- `d1`: 从数据源（localhost:3316）

### 包结构
- `config/`: 各种配置类
- `controller/`: REST控制器
- `service/`: 业务逻辑层
- `mapper/`: MyBatis映射器
- `model/`: 实体类
- `job/`: XXL-Job任务实现
- `handler/`: 全局异常处理等
- `enums/`: 枚举类

### 关键注解使用
- `@DSTransactional`: 动态数据源事务注解
- `@DS("d1")`: 指定使用特定数据源
- `@MapperScan`: MyBatis映射器扫描

## 开发环境要求

- Java 1.8
- Maven 3.x
- MySQL（端口13306和3316）
- ElasticSearch 7.4.2（localhost:9200）
- XXL-Job Admin（localhost:9001）

## 测试接口

项目提供了多个测试接口：
- `/testPerson`: 测试MyBatis-Plus查询
- `/testEs`: 测试ElasticSearch查询
- `/testAdd`: 测试ES文档添加
- `/testBulk`: 测试ES批量操作

## 注意事项

- 配置文件中包含数据库密码等敏感信息，实际部署时需要外部化配置
- ES客户端版本固定为7.4.2，确保与服务端版本兼容
- XXL-Job需要先启动调度中心再启动执行器