package com.von.demo1;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/8/2 15:08
 * @Version 1.0
 */
public class GeneralTest {

    class Author {
        private String name;
        private List<String> books;
        // 构造函数, getter...

        public Author(String name, List<String> books) {
            this.name = name;
            this.books = books;
        }

        public List<String> getBooks() {
            return books;
        }
    }

    @Test
    public void test11() throws Exception {
        List<List<Integer>> listOfLists = Arrays.asList(
                Arrays.asList(4, 5, 6),
                Arrays.asList(1, 2, 3),
                Arrays.asList(7, 8, 9)
        );
        List<Integer> collect = listOfLists.stream().flatMap(List::stream).collect(Collectors.toList());
        System.out.println(collect);


    }

    @Test
    public void test45() throws Exception {
        List<Author> authors = Arrays.asList(
                new Author("作者A", Arrays.asList("书1", "书2")),
                new Author("作者B", Arrays.asList("书3", "书4", "书5")),
                new Author("作者C", Arrays.asList("书6"))
        );
        List<String> collect = authors.stream().map(Author::getBooks).flatMap(List::stream).collect(Collectors.toList());
        System.out.println(collect);
    }

    @Test
    public void test56() throws Exception {
        List<String> sentences = Arrays.asList(
                "hello world",
                "java stream flatmap"
        );
        List<String> collect = sentences.stream().flatMap(s -> Arrays.stream(s.split(" "))).collect(Collectors.toList());
        System.out.println(collect);
    }



    @Test
    public void test86() throws Exception {
        List<String> list = Arrays.asList("A", "B", "C");
        String collect = list.stream().collect(Collectors.joining("-"));
        System.out.println(collect);
    }

    @Test
    public void test76() throws Exception {
        List<Person> personList = new ArrayList<Person>();

        personList.add(new Person("Sherry", 9000, 24, "female", "New York"));
        personList.add(new Person("Tom", 8900, 22, "male", "Washington"));
        personList.add(new Person("Jack", 9000, 25, "male", "Washington"));
        personList.add(new Person("Lily", 8800, 26, "male", "New York"));
        personList.add(new Person("Alisa", 9000, 26, "female", "New York"));
        List<Person> collect = personList.stream()
                .sorted(Comparator.comparing(Person::getSalary, Comparator.reverseOrder()).thenComparing(Person :: getAge))
                .collect(Collectors.toList());
        System.out.println(collect);


    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Person {
        private String name;  // 姓名
        private int salary; // 薪资
        private Integer age; // 年龄
        private String sex; //性别
        private String area;  // 地区


    }

}
