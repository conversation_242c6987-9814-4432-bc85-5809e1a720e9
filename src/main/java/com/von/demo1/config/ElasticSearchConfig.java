package com.von.demo1.config;

import org.apache.http.HttpHost;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 *
 * <AUTHOR>
 * @Date 2024/11/5 上午11:31
 * @Version 1.0
 */
@Configuration
public class ElasticSearchConfig {

    @Value("${von.es.host}")
    private String host;

    @Value("${von.es.port}")
    private int port;

    @Value("${von.es.scheme}")
    private String scheme;

    public static final RequestOptions COMMON_OPTIONS;

    static {
        RequestOptions.Builder builder = RequestOptions.DEFAULT.toBuilder();
        COMMON_OPTIONS = builder.build();
    }

    @Bean
    public RestHighLevelClient esRestClient() {
        RestClientBuilder restClientBuilder = RestClient.builder(new HttpHost(host, port, scheme));
        return new RestHighLevelClient(restClientBuilder);
    }

}
