package com.von.demo1.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.demo1.mapper.TUserMobilePlanMapper;
import com.von.demo1.model.TUserMobilePlanEntity;
import com.von.demo1.service.TUserMobilePlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
public class TUserMobilePlanServiceImpl extends ServiceImpl<TUserMobilePlanMapper, TUserMobilePlanEntity> implements TUserMobilePlanService {

    private final TUserMobilePlanMapper tUserMobilePlanMapper;

}
 