package com.von.demo1.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/18 0:40
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SexEnum {

    MALE(1, "男"),

    FEMALE(2, "女");

    private final Integer code;

    private final String msg;

    private static final Map<Integer, SexEnum> SEX_ENUM_MAP = new HashMap<>();

    static {
        for (SexEnum sexEnum : SexEnum.values()) {
            SEX_ENUM_MAP.put(sexEnum.getCode(), sexEnum);
        }
    }

    public static SexEnum getSexEnum(Integer code) {
        return SEX_ENUM_MAP.get(code);
    }

}
