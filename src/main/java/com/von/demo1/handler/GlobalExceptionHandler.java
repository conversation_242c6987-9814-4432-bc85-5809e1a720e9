package com.von.demo1.handler;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

/**
 * <AUTHOR>
 * @Date 2025/6/13 1:06
 * @Version 1.0
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    // 处理所有未捕获的异常
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleAllExceptions(
            Exception ex, WebRequest request) {

        // 创建错误响应对象（实际项目中可定义ErrorResponse类）
        String errorMessage = "发生系统错误: " + ex.getMessage();

        return new ResponseEntity<>(
                errorMessage,
                HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

}
