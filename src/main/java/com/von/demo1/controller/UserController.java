package com.von.demo1.controller;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.von.demo1.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * User Controller
 * 
 * <AUTHOR>
 * @Date 2025/08/03
 * @Version 1.0
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class UserController {

    private final IUserService userService;

    @GetMapping("/testUser2")
    @DSTransactional()
    public Object testUser2() throws Exception {
        return this.userService.list();
    }
}