package com.von.demo1.controller;

import com.von.demo1.job.SimpleXxlJob;
import com.von.demo1.service.TUserMobilePlanService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tUserMobilePlan")
@Tag(description = "接口", name = "接口")
public class TUserMobilePlanController {

    private final TUserMobilePlanService tUserMobilePlanService;

    private final SimpleXxlJob simpleXxlJob;

    @GetMapping("/list")
    public Object list() throws Exception {
        this.simpleXxlJob.sendMsgHandler();

        return "test";
    }

} 