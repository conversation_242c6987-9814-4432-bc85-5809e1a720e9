package com.von.demo1.job;

import com.von.demo1.mapper.TUserMobilePlanMapper;
import com.von.demo1.model.TUserMobilePlanEntity;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/6/23 0:54
 * @Version 1.0
 */
@Component
@RequiredArgsConstructor
public class SimpleXxlJob {

    private final TUserMobilePlanMapper userMobilePlanMapper;

    @XxlJob("sendMsgHandler")
    public void sendMsgHandler() throws Exception{
        List<TUserMobilePlanEntity> userMobilePlans = userMobilePlanMapper.selectList(null);
        System.out.println("任务开始时间:"+new Date()+",处理任务数量:"+userMobilePlans.size());
        Long startTime = System.currentTimeMillis();
        userMobilePlans.forEach(item->{
            try {
                //模拟发送短信动作
                TimeUnit.MILLISECONDS.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
        System.out.println("任务结束时间:"+new Date());
        System.out.println("任务耗时:"+(System.currentTimeMillis()-startTime)+"毫秒");
    }

    @XxlJob("testShare")
    public void testShare() {
        XxlJobHelper.log("任务开始时间 : " + new Date());
        Long startTime = System.currentTimeMillis();
        int index = XxlJobHelper.getShardIndex();
        int total = XxlJobHelper.getShardTotal();
        List<TUserMobilePlanEntity> userList = new ArrayList<>();
        if (total == 1) {
            userList = this.userMobilePlanMapper.selectList(null);
        }
        userList = this.userMobilePlanMapper.selectByMode(index, total);
        XxlJobHelper.log("处理的任务数量 : " + userList.size());
        userList.forEach(item->{
            try {
                //模拟发送短信动作
                TimeUnit.MILLISECONDS.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
        XxlJobHelper.log("任务结束时间:"+new Date());
        XxlJobHelper.log("任务耗时:"+(System.currentTimeMillis()-startTime)+"毫秒");
    }

}
