package com.von.demo1.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Data
@TableName("t_user_mobile_plan")
@Schema(description = "")
@EqualsAndHashCode(callSuper = true)
public class TUserMobilePlanEntity extends Model<TUserMobilePlanEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:${field.metaInfo.jdbcType} &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释: &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:${field.metaInfo.jdbcType} &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释: &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "")
    @JsonProperty("username")
    @TableField("username")
    private String username;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:${field.metaInfo.jdbcType} &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释: &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "")
    @JsonProperty("nickname")
    @TableField("nickname")
    private String nickname;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:${field.metaInfo.jdbcType} &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释: &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "")
    @JsonProperty("phone")
    @TableField("phone")
    private String phone;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:${field.metaInfo.jdbcType} &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释: &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "")
    @JsonProperty("info")
    @TableField("info")
    private String info;


} 