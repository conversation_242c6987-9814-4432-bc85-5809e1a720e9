spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          username: root
          password: aa545554555..
          #url: ********************************************************************************************************************
          url: ****************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
        d1:
          username: root
          password: aa545554555..
          url: ***************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 下划线转驼峰
    map-underscore-to-camel-case: true
    # SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0