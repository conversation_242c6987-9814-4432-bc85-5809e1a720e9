# MCP (Model Context Protocol) 部署使用教程

## 什么是MCP？

MCP (Model Context Protocol) 是一个开源标准，用于AI工具集成。它允许AI助手（如Claude Code、Cursor等）连接到外部工具、数据库和API，大大扩展AI的能力范围。

## 百度MCP导航网站介绍

根据你提供的链接 `https://sai.baidu.com/zh/detail/faef7e83d5acad90dfafbcbea38e8a3e`，这是百度推出的"海量MCP Servers导航网站"，专门收录和展示各种MCP服务器。

该页面展示的是**语雀MCP服务器**，具有以下特点：
- **产品定位**: 与语雀API集成的MCP服务器，提供与语雀知识库平台交互的工具
- **核心功能**: 
  - 获取用户和文档信息
  - 创建、读取、更新和删除文档
  - 搜索语雀中的内容
  - 获取知识库信息
  - 获取统计数据和分析信息

## 一、在Claude Code中部署MCP

### 1.1 基本语法

Claude Code原生支持MCP，可以通过命令行轻松添加MCP服务器：

```bash
# 添加本地stdio服务器
claude mcp add <服务器名称> <命令> [参数...]

# 添加远程SSE服务器
claude mcp add --transport sse <服务器名称> <URL>

# 添加远程HTTP服务器
claude mcp add --transport http <服务器名称> <URL>
```

### 1.2 常用MCP服务器部署示例

#### 1.2.1 开发工具类

```bash
# Sentry错误监控
claude mcp add --transport http sentry https://mcp.sentry.dev/sse

# Socket安全分析
claude mcp add --transport http socket https://mcp.socket.dev/

# GitHub集成
claude mcp add github --env GITHUB_PERSONAL_ACCESS_TOKEN=your_token -- npx -y @modelcontextprotocol/server-github
```

#### 1.2.2 项目管理类

```bash
# Linear问题跟踪
claude mcp add --transport sse linear https://mcp.linear.app/sse

# Notion文档管理
claude mcp add --transport http notion https://mcp.notion.com/mcp

# Asana项目管理
claude mcp add --transport sse asana https://mcp.asana.com/sse

# Atlassian (Jira/Confluence)
claude mcp add --transport sse atlassian https://mcp.atlassian.com/v1/sse
```

#### 1.2.3 数据库类

```bash
# PostgreSQL数据库
claude mcp add postgres --env POSTGRES_CONNECTION_STRING=your_connection_string -- npx -y @modelcontextprotocol/server-postgres

# SQLite数据库
claude mcp add sqlite --env SQLITE_DB_PATH=/path/to/database.db -- npx -y @modelcontextprotocol/server-sqlite
```

#### 1.2.4 语雀MCP服务器部署

基于百度网站展示的语雀MCP服务器，部署方式：

```bash
# 克隆仓库
git clone https://github.com/Henryhaoson/Yueque-MCP-Server.git
cd Yueque-MCP-Server

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑.env文件，添加语雀API令牌
echo "YUQUE_API_TOKEN=your_yuque_api_token_here" >> .env

# 添加到Claude Code
claude mcp add yuque --env YUQUE_API_TOKEN=your_token -- node /path/to/Yueque-MCP-Server/dist/index.js
```

### 1.3 管理MCP服务器

```bash
# 查看所有已配置的服务器
claude mcp list

# 查看特定服务器详情
claude mcp get <服务器名称>

# 删除服务器
claude mcp remove <服务器名称>

# 在Claude Code中检查服务器状态
/mcp
```

### 1.4 配置作用域

MCP服务器可以配置在不同作用域：

```bash
# 本地作用域（默认，仅当前项目）
claude mcp add --scope local server-name command

# 项目作用域（团队共享，存储在.mcp.json）
claude mcp add --scope project server-name command

# 用户作用域（跨项目使用）
claude mcp add --scope user server-name command
```

## 二、在其他AI软件中使用MCP

### 2.1 Cursor IDE

Cursor IDE也支持MCP集成：

1. 安装MCP服务器到本地
2. 在Cursor设置中配置MCP服务器
3. 使用`@`符号引用MCP资源

### 2.2 VS Code with GitHub Copilot

```json
// settings.json
{
  "github.copilot.chat.mcpServers": {
    "server-name": {
      "command": "path/to/server",
      "args": ["--arg1", "value1"]
    }
  }
}
```

### 2.3 Claude Desktop

在`claude_desktop_config.json`中配置：

```json
{
  "mcpServers": {
    "server-name": {
      "command": "path/to/server",
      "args": [],
      "env": {
        "API_KEY": "your-api-key"
      }
    }
  }
}
```

## 三、实际使用示例

### 3.1 使用语雀MCP服务器

```bash
# 部署后，你可以这样使用：
> "帮我获取语雀中关于API文档的所有文章"
> "在我的语雀知识库中创建一个新的技术文档"
> "搜索语雀中包含'MCP'关键词的文档"
> "获取我们团队的语雀使用统计数据"
```

### 3.2 使用Sentry监控

```bash
# 部署Sentry MCP后：
> "查看过去24小时内最常见的错误"
> "显示错误ID abc123的堆栈跟踪"
> "哪个部署引入了这些新错误？"
```

### 3.3 使用数据库MCP

```bash
# 部署数据库MCP后：
> "查询用户表中最近注册的10个用户"
> "分析订单表中的销售趋势"
> "创建一个新的数据表用于存储日志"
```

## 四、安全注意事项

1. **谨慎使用第三方MCP服务器** - 确保信任你安装的MCP服务器
2. **保护API密钥** - 使用环境变量存储敏感信息
3. **限制权限** - 只给MCP服务器必要的权限
4. **定期更新** - 保持MCP服务器版本最新

## 五、故障排除

### 5.1 常见问题

1. **连接失败**: 检查网络连接和API密钥
2. **权限错误**: 确认API令牌有足够权限
3. **超时问题**: 使用`MCP_TIMEOUT`环境变量调整超时时间

### 5.2 调试命令

```bash
# 设置超时时间
MCP_TIMEOUT=10000 claude

# 查看详细日志
claude --verbose mcp list

# 重置项目选择
claude mcp reset-project-choices
```

## 六、推荐的MCP服务器

基于百度MCP导航网站的推荐：

1. **语雀MCP服务器** - 知识库管理
2. **Linear MCP服务** - 问题跟踪
3. **Atlassian智能集成服务** - Jira/Confluence
4. **AWS文档MCP服务器** - AWS文档查询
5. **ClickUp任务管理协议服务器** - 任务管理

## 总结

MCP为AI助手提供了强大的扩展能力，通过合理配置和使用MCP服务器，可以大大提升开发效率和AI助手的实用性。建议从简单的服务器开始，逐步扩展到更复杂的集成场景。
